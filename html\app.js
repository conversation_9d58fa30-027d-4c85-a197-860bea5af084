const chatContainer = document.getElementById('chat-container');
const chatInput = document.getElementById('chat-input');

// Command history
let commandHistory = [];
let historyIndex = -1;
const MAX_HISTORY = 20; // Maximum number of commands to store in history

// Function to send a command
function sendCommand() {
    const command = chatInput.value.trim();
    if (command !== '') {
        // Add command to history
        if (command !== commandHistory[0]) {
            commandHistory.unshift(command); // Add to beginning of array
            if (commandHistory.length > MAX_HISTORY) {
                commandHistory.pop(); // Remove oldest command if we exceed max
            }
        }
        historyIndex = -1; // Reset history index

        // Execute the command (no need to check for slash since it's fixed in the UI)
        fetch(`https://${GetParentResourceName()}/executeCommand`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json; charset=UTF-8',
            },
            body: JSON.stringify({
                command: command
            })
        }).then(resp => resp.json()).then(() => {
            console.log('Command executed');
        });

        chatInput.value = '';
    } else {
        closeChat();
    }
}

// Function to open the chat
function openChat() {
    chatContainer.classList.remove('fade-out');
    chatContainer.classList.add('fade-in');
    // Focus the input immediately and also after a short delay to ensure it works
    chatInput.focus();
    setTimeout(() => {
        chatInput.focus();
    }, 50);
}

// Function to close the chat
function closeChat() {
    chatContainer.classList.remove('fade-in');
    chatContainer.classList.add('fade-out');
    setTimeout(() => {
        chatContainer.style.display = 'none';
        fetch(`https://${GetParentResourceName()}/close`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json; charset=UTF-8',
            },
            body: JSON.stringify({})
        });
    }, 300);
}

// Function to navigate command history
function navigateHistory(direction) {
    if (commandHistory.length === 0) return;

    if (direction === 'up') {
        historyIndex = Math.min(historyIndex + 1, commandHistory.length - 1);
    } else if (direction === 'down') {
        historyIndex = Math.max(historyIndex - 1, -1);
    }

    if (historyIndex >= 0) {
        chatInput.value = commandHistory[historyIndex];
        // Place cursor at the end of the input
        setTimeout(() => {
            chatInput.selectionStart = chatInput.selectionEnd = chatInput.value.length;
        }, 0);
    } else {
        chatInput.value = '';
    }
}

// Event listeners
chatInput.addEventListener('keydown', (e) => {
    if (e.key === 'Enter') {
        sendCommand();
    } else if (e.key === 'Escape') {
        closeChat();
    } else if (e.key === 'ArrowUp') {
        e.preventDefault(); // Prevent cursor from moving to start of input
        navigateHistory('up');
    } else if (e.key === 'ArrowDown') {
        e.preventDefault(); // Prevent cursor from moving to end of input
        navigateHistory('down');
    }
});

// NUI message handler
window.addEventListener('message', (event) => {
    const data = event.data;

    switch (data.type) {
        case 'OPEN_CHAT':
            openChat();
            break;
        case 'CLOSE_CHAT':
            closeChat();
            break;
    }
});

// Prevent form submission
document.addEventListener('submit', (e) => {
    e.preventDefault();
});
