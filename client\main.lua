local QBCore = exports['qb-core']:GetCoreObject()
local chatInputActive = false
local chatInputActivating = false

-- Function to open the chat UI
function openChat()
    if not chatInputActive then
        chatInputActive = true
        SendNUIMessage({
            type = "OPEN_CHAT"
        })
        SetNuiFocus(true, false) -- Set mouse focus to false, keyboard focus to true
    end
end

-- Function to close the chat UI
function closeChat()
    if chatInputActive then
        chatInputActive = false
        SendNUIMessage({
            type = "CLOSE_CHAT"
        })
        SetNuiFocus(false, false)
    end
end

-- Register command to open chat
RegisterCommand('qbchat', function()
    openChat()
end, false)

-- Register keybinding to open chat
RegisterKeyMapping('qbchat', 'Open QB Chat', 'keyboard', 'T')

-- Create a thread to handle the chat visibility
Citizen.CreateThread(function()
    while true do
        Citizen.Wait(0)
        if IsControlJustPressed(0, 245) then -- T key
            openChat()
        end
    end
end)

-- NUI Callbacks
RegisterNUICallback('close', function(data, cb)
    closeChat()
    cb('ok')
end)

RegisterNUICallback('executeCommand', function(data, cb)
    if data.command then
        ExecuteCommand(data.command)
    end
    closeChat()
    cb('ok')
end)
