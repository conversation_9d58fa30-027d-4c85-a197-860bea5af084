# QB-Chat

A minimalist chat interface for QBCore commands.

## Features

- Clean, minimal UI with custom colors (#E2E2E2, #C73659, #A91D3A, #262626, #111111)
- Rectangular design with stylish borders and subtle glow effect
- Small size, positioned above the minimap
- Support for QBCore commands
- Command history navigation with up/down arrow keys
- Simple input box with Enter key execution
- Keyboard-only focus (no mouse cursor) for distraction-free typing

## Usage

1. Press `T` to open the chat interface
2. Type your command (with or without `/` prefix)
3. Press Enter to execute the command
4. Use Up/Down arrow keys to navigate through command history
5. Press Escape to close the chat

## Installation

1. Place the `qb-chat` folder in your server's resources directory
2. Add `ensure qb-chat` to your server.cfg
3. Restart your server

## Customization

You can customize the appearance by editing the `style.css` file in the `html` folder.

## Dependencies

- QBCore Framework

## Credits

Created for QBCore Framework
