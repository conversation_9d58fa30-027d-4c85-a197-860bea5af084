@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600&display=swap');

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Poppins', sans-serif;
}

body {
    overflow: hidden;
}

#chat-container {
    position: absolute;
    bottom: 240px; /* Position even higher above the minimap */
    left: 25px; /* Moved slightly to the right */
    width: 180px; /* Smaller size */
    display: none;
    z-index: 1000;
    transform: rotate(-1deg); /* Add a slight tilt */
}

#chat-input-container {
    display: flex;
    padding: 6px 8px; /* Smaller padding */
    background: rgba(0, 0, 0, 0.7); /* Much darker background */
    border: none;
    border-radius: 3px;
    position: relative;
    overflow: hidden;
    transform: perspective(300px) rotateX(1deg);

    /* Darker glass effect */
    backdrop-filter: blur(2px);
    -webkit-backdrop-filter: blur(2px);

    /* Sharper shadows for defined edges */
    box-shadow:
        0 0 5px rgba(0, 0, 0, 0.8), /* Darker outer shadow */
        inset 0 0 2px rgba(199, 54, 89, 0.8), /* Inner red glow */
        0 0 0 1px rgba(169, 29, 58, 0.8); /* Sharp edge outline */
}

/* Sharp edge styling */
#chat-input-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border: 1px solid #A91D3A; /* Solid border for sharper edges */
    border-radius: 3px;
    pointer-events: none;
    z-index: 3;
    box-shadow: inset 0 0 1px #C73659; /* Inner glow */
}

/* Add corner accents - smaller and sharper */
#chat-input-container::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        linear-gradient(135deg, rgba(199, 54, 89, 1) 0%, transparent 6%),  /* Top-left corner */
        linear-gradient(-135deg, rgba(199, 54, 89, 1) 0%, transparent 6%), /* Top-right corner */
        linear-gradient(-45deg, rgba(199, 54, 89, 1) 0%, transparent 6%),  /* Bottom-right corner */
        linear-gradient(45deg, rgba(199, 54, 89, 1) 0%, transparent 6%);   /* Bottom-left corner */
    pointer-events: none;
    z-index: 2;
}

.command-prefix {
    color: #C73659;
    font-size: 14px;
    font-weight: bold;
    padding: 4px 0 4px 8px;
    text-shadow: 0 0 3px rgba(199, 54, 89, 0.7);
    z-index: 4;
}

#chat-input {
    flex: 1;
    padding: 4px 4px 4px 2px; /* Reduced left padding */
    border: none;
    background-color: transparent;
    color: #E2E2E2;
    outline: none;
    font-size: 12px; /* Smaller font */
    letter-spacing: 0.3px;
    text-shadow: 0 0 1px rgba(0, 0, 0, 0.9), 0 0 3px rgba(199, 54, 89, 0.5); /* Darker shadow */
    position: relative;
    z-index: 4;
    caret-color: #C73659;
}

#chat-input::placeholder {
    color: rgba(226, 226, 226, 0.4); /* Darker placeholder */
    font-style: italic;
    letter-spacing: 0.3px;
}

/* Add a small indicator for command history */
.history-indicator {
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    width: 4px; /* Smaller size */
    height: 4px;
    border-radius: 50%;
    background: #A91D3A; /* Darker red */
    box-shadow: 0 0 3px rgba(199, 54, 89, 0.9);
    opacity: 0.9;
    z-index: 5;
}

/* Animation for opening and closing */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(5px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes fadeOut {
    from { opacity: 1; transform: translateY(0); }
    to { opacity: 0; transform: translateY(5px); }
}

@keyframes darkPulse {
    0% {
        box-shadow:
            0 0 5px rgba(0, 0, 0, 0.8),
            inset 0 0 2px rgba(199, 54, 89, 0.8),
            0 0 0 1px rgba(169, 29, 58, 0.8);
        transform: perspective(300px) rotateX(1deg);
    }
    50% {
        box-shadow:
            0 0 5px rgba(0, 0, 0, 0.9),
            inset 0 0 3px rgba(199, 54, 89, 1),
            0 0 0 1px rgba(169, 29, 58, 1);
        transform: perspective(300px) rotateX(1.2deg);
    }
    100% {
        box-shadow:
            0 0 5px rgba(0, 0, 0, 0.8),
            inset 0 0 2px rgba(199, 54, 89, 0.8),
            0 0 0 1px rgba(169, 29, 58, 0.8);
        transform: perspective(300px) rotateX(1deg);
    }
}

.fade-in {
    display: block !important;
    animation: fadeIn 0.2s ease-out forwards;
}

.fade-in #chat-input-container {
    animation: darkPulse 3s infinite;
}

.fade-out {
    animation: fadeOut 0.2s ease-in forwards;
}
